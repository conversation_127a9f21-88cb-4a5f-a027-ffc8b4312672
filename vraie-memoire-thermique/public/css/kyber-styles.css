/**
 * Styles pour l'accélérateur Kyber et ses composants d'interface
 */

/* Indicateur Kyber dans l'en-tête */
.kyber-status {
    display: flex;
    align-items: center;
    margin-left: 20px;
    padding: 5px 12px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    font-size: 14px;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.active {
    background-color: #2ecc71;
    box-shadow: 0 0 8px #2ecc71;
    animation: pulse 2s infinite;
}

.status-indicator.disabled {
    background-color: #e74c3c;
    box-shadow: 0 0 3px #e74c3c;
}

.status-indicator.boost {
    background-color: #f39c12;
    box-shadow: 0 0 12px #f39c12;
    animation: pulse-fast 0.8s infinite;
}

#kyber-label {
    font-weight: 600;
    margin-right: 10px;
}

.kyber-boost-btn {
    background: linear-gradient(to right, #e74c3c, #f39c12);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.5);
}

.kyber-boost-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.8);
}

.kyber-boost-btn:active {
    transform: scale(0.95);
}

.kyber-boost-btn.active {
    background: linear-gradient(to right, #f39c12, #f1c40f);
    animation: pulse-boost 1s infinite;
}

/* Statistiques système dans le pied de page */
.system-stats {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

#kyber-temperature, #memory-utilization, #kyber-acceleration {
    font-weight: 600;
}

#kyber-temperature.high {
    color: #e74c3c;
}

#memory-utilization.high {
    color: #e67e22;
}

#kyber-acceleration.boosted {
    color: #f39c12;
    font-weight: 700;
    text-shadow: 0 0 3px rgba(243, 156, 18, 0.6);
}

/* Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 5px #2ecc71;
    }
    50% {
        box-shadow: 0 0 10px #2ecc71;
    }
    100% {
        box-shadow: 0 0 5px #2ecc71;
    }
}

@keyframes pulse-fast {
    0% {
        box-shadow: 0 0 5px #f39c12;
    }
    50% {
        box-shadow: 0 0 15px #f39c12;
    }
    100% {
        box-shadow: 0 0 5px #f39c12;
    }
}

@keyframes pulse-boost {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}

/* Interface de la section Mémoire */
.memory-level-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    gap: 10px;
}

.memory-level-btn {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 5px;
    background-color: rgba(52, 152, 219, 0.2);
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.memory-level-btn:hover {
    background-color: rgba(52, 152, 219, 0.4);
}

.memory-level-btn.active {
    background-color: #3498db;
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.7);
}

.memory-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid #3498db;
    position: relative;
    animation: fade-in 0.3s ease-in-out;
}

.memory-item.important {
    border-left-color: #e74c3c;
}

.memory-item-header {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 5px;
}

.memory-item-importance {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    background-color: rgba(52, 152, 219, 0.3);
}

.memory-item-importance.high {
    background-color: rgba(231, 76, 60, 0.3);
}

.memory-item-content {
    color: #fff;
    line-height: 1.5;
}

.memory-item-timestamp {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    margin-top: 8px;
    text-align: right;
}

@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Améliorations du style de l'activité cérébrale */
.thought-item {
    padding: 10px 15px;
    margin-bottom: 12px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.05);
    border-left: 3px solid #3498db;
    position: relative;
    transition: all 0.3s ease;
}

.thought-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
}

.thought-item.reasoning {
    border-left-color: #3498db;
}

.thought-item.memory {
    border-left-color: #9b59b6;
}

.thought-item.planning {
    border-left-color: #2ecc71;
}

.thought-item.learning {
    border-left-color: #f39c12;
}

.thought-item.security {
    border-left-color: #e74c3c;
}

.thought-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.thought-category {
    text-transform: uppercase;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    background-color: rgba(52, 152, 219, 0.2);
}

.thought-category.reasoning {
    background-color: rgba(52, 152, 219, 0.2);
}

.thought-category.memory {
    background-color: rgba(155, 89, 182, 0.2);
}

.thought-category.planning {
    background-color: rgba(46, 204, 113, 0.2);
}

.thought-category.learning {
    background-color: rgba(243, 156, 18, 0.2);
}

.thought-category.security {
    background-color: rgba(231, 76, 60, 0.2);
}

.thought-content {
    line-height: 1.5;
    color: #fff;
    margin-bottom: 5px;
}

.thought-source {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    text-align: right;
}

/* Kyber section styles */
.kyber-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.kyber-boost-control {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.boost-slider-container {
    width: 100%;
    margin: 15px 0;
}

.boost-slider {
    width: 100%;
    -webkit-appearance: none;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #3498db, #e74c3c);
    outline: none;
}

.boost-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #f39c12;
    cursor: pointer;
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.8);
}

.boost-value {
    font-size: 18px;
    font-weight: 600;
    margin: 10px 0;
    color: #f39c12;
}

.boost-activate-btn {
    background: linear-gradient(to right, #e74c3c, #f39c12);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
}

.boost-activate-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(243, 156, 18, 0.5);
}

.kyber-metrics {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 15px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.metric-label {
    color: rgba(255, 255, 255, 0.7);
}

.metric-value {
    font-weight: 600;
}

.temperature-value {
    color: #2ecc71;
}

.temperature-value.warm {
    color: #f39c12;
}

.temperature-value.hot {
    color: #e74c3c;
}

.stats-graph-container {
    width: 100%;
    height: 180px;
    margin-top: 20px;
}

.kyber-temp-gauge {
    width: 150px;
    height: 150px;
    margin: 0 auto;
    position: relative;
}

.gauge-background {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(#e74c3c 0% 33%, #f39c12 33% 66%, #2ecc71 66% 100%);
    mask: radial-gradient(transparent 55%, black 56%);
    -webkit-mask: radial-gradient(transparent 55%, black 56%);
}

.gauge-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    font-weight: 700;
    color: white;
}
