/* CSS principal pour l'interface de mémoire thermique */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

.app-wrapper {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 300px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
    color: #ff69b4;
    font-size: 1.2rem;
}

.main-content {
    margin-left: 300px;
    flex: 1;
    padding: 20px;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2rem;
    color: #ff69b4;
}

.logo h1 {
    font-size: 2rem;
    background: linear-gradient(45deg, #ff69b4, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

#app-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.8rem;
}

#app-status.connected {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid #4caf50;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

nav a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

nav a:hover,
nav a.active {
    background: rgba(255, 105, 180, 0.2);
    color: #ff69b4;
    transform: translateY(-2px);
}

main {
    min-height: 600px;
}

section {
    display: none;
}

section.active {
    display: block;
}

.card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 105, 180, 0.2);
    border-color: rgba(255, 105, 180, 0.3);
}

.card h3 {
    color: #ff69b4;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
}

.btn {
    background: linear-gradient(135deg, #ff69b4, #e91e63);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.btn.primary {
    background: linear-gradient(135deg, #2196f3, #1976d2);
}

.btn.success {
    background: linear-gradient(135deg, #4caf50, #388e3c);
}

.btn.warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.badge.primary {
    background: rgba(33, 150, 243, 0.2);
    color: #2196f3;
    border: 1px solid #2196f3;
}

.badge.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid #4caf50;
}

.badge.warning {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
    border: 1px solid #ff9800;
}

.badge.info {
    background: rgba(0, 188, 212, 0.2);
    color: #00bcd4;
    border: 1px solid #00bcd4;
}

.badge.accent {
    background: rgba(255, 105, 180, 0.2);
    color: #ff69b4;
    border: 1px solid #ff69b4;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.justify-end {
    justify-content: flex-end;
}

.items-center {
    align-items: center;
}

.gap-md {
    gap: 15px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.neural-visualization {
    height: 400px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.neural-visualization.large {
    height: 500px;
}

/* Responsive */
@media (max-width: 1200px) {
    .grid-3 {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .grid-2,
    .grid-3 {
        grid-template-columns: 1fr;
    }
    
    nav ul {
        flex-direction: column;
        gap: 10px;
    }
}
