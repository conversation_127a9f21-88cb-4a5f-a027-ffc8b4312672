/**
 * Design futuriste et moderne pour l'Agent à Mémoire Thermique
 * Interface révolutionnaire avec visualisations avancées
 */

:root {
    /* Palette de couleurs principale */
    --primary-dark: #0a1929;
    --primary: #0d47a1;
    --primary-light: #5e92f3;
    --secondary: #00bcd4;
    --accent: #00e5ff;
    --accent-glow: rgba(0, 229, 255, 0.4);

    /* Couleurs fonctionnelles */
    --success: #00e676;
    --warning: #ffab00;
    --danger: #ff1744;
    --info: #00b0ff;

    /* Couleurs de fond et texte */
    --bg-dark: #0a1929;
    --bg-main: #0f2942;
    --bg-light: #1a3b5d;
    --bg-card: rgba(26, 59, 93, 0.7);
    --bg-card-hover: rgba(30, 70, 110, 0.8);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-muted: rgba(255, 255, 255, 0.5);

    /* Effets et bordures */
    --glow-effect: 0 0 15px var(--accent-glow);
    --border-light: 1px solid rgba(255, 255, 255, 0.1);
    --border-accent: 1px solid rgba(0, 229, 255, 0.3);
    --border-radius-sm: 6px;
    --border-radius: 10px;
    --border-radius-lg: 16px;

    /* Taille de police */
    --font-size-factor: 1;

    /* Ombres */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.2);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.3);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Dimensions */
    --sidebar-width: 320px;
    --header-height: 60px;
    --footer-height: 40px;
}

/* Styles de base */
body {
    font-family: 'Roboto', 'Segoe UI', sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    overflow: hidden;
    line-height: 1.6;
}

/* Structure principale */
.app-wrapper {
    display: flex;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-main) 100%);
}

/* Barre latérale futuriste */
.sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, var(--bg-main) 0%, var(--bg-dark) 100%);
    border-right: var(--border-light);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 100;
    transition: width var(--transition);
    overflow: hidden;
}

/* Effet de bordure lumineuse sur la barre latérale */
.sidebar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(0, 229, 255, 0) 0%,
        rgba(0, 229, 255, 0.5) 50%,
        rgba(0, 229, 255, 0) 100%);
    box-shadow: 0 0 15px var(--accent-glow);
}

/* En-tête de la barre latérale */
.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: var(--border-light);
    background: rgba(10, 25, 41, 0.7);
    backdrop-filter: blur(10px);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--accent);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-header h3 i {
    font-size: 1.4rem;
    color: var(--accent);
    text-shadow: 0 0 10px var(--accent-glow);
}

/* Sections de la barre latérale */
.sidebar-section {
    padding: 20px;
    border-bottom: var(--border-light);
    background: rgba(15, 41, 66, 0.4);
}

.sidebar-section h4 {
    margin: 0 0 15px 0;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-section h4 i {
    color: var(--accent);
}

/* Indicateurs d'activité */
.activity-indicators {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.activity-indicator {
    background: rgba(26, 59, 93, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
    padding: 12px;
    display: flex;
    flex-direction: column;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.activity-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--accent), transparent);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.activity-indicator:hover {
    background: rgba(30, 70, 110, 0.5);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.activity-indicator:hover::before {
    opacity: 1;
}

.indicator-name {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.indicator-name i {
    font-size: 0.85rem;
}

.indicator-value {
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.indicator-value.active {
    color: var(--success);
    text-shadow: 0 0 10px rgba(0, 230, 118, 0.4);
}

.indicator-value.inactive {
    color: var(--danger);
    text-shadow: 0 0 10px rgba(255, 23, 68, 0.4);
}

.indicator-value.medium {
    color: var(--warning);
    text-shadow: 0 0 10px rgba(255, 171, 0, 0.4);
}

/* Couleurs spécifiques pour les indicateurs */
.oral-indicator .indicator-value.active {
    color: var(--info);
    text-shadow: 0 0 10px rgba(0, 176, 255, 0.4);
}

.written-indicator .indicator-value.active {
    color: var(--success);
    text-shadow: 0 0 10px rgba(0, 230, 118, 0.4);
}

.accelerator-indicator .indicator-value.active {
    color: var(--warning);
    text-shadow: 0 0 10px rgba(255, 171, 0, 0.4);
}

.visual-indicator .indicator-value.active {
    color: var(--secondary);
    text-shadow: 0 0 10px rgba(0, 188, 212, 0.4);
}

/* Animation neuronale dans la barre latérale */
.neural-activity-sidebar {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: rgba(10, 25, 41, 0.7);
    border-top: var(--border-light);
}

/* Contenu principal */
.main-content {
    flex: 1;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, var(--bg-main) 0%, var(--bg-dark) 100%);
    position: relative;
}

/* Effet de grille futuriste en arrière-plan */
.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 229, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 229, 255, 0.03) 1px, transparent 1px);
    background-size: 40px 40px;
    z-index: 0;
    pointer-events: none;
}

/* Conteneur de l'application */
.app-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

/* En-tête principal */
header {
    height: var(--header-height);
    padding: 0 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(10, 25, 41, 0.7);
    backdrop-filter: blur(10px);
    border-bottom: var(--border-light);
    position: relative;
    z-index: 10;
}

/* Logo et titre */
.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 1.8rem;
    color: var(--accent);
    text-shadow: 0 0 15px var(--accent-glow);
}

.logo h1 {
    font-size: 1.4rem;
    font-weight: 500;
    margin: 0;
    background: linear-gradient(to right, var(--text-primary), var(--accent));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 1px;
}

/* Statut de l'application */
#app-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

#app-status::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

#app-status.connected {
    background: rgba(0, 230, 118, 0.1);
    color: var(--success);
}

#app-status.connected::before {
    background-color: var(--success);
    box-shadow: 0 0 8px var(--success);
}

#app-status.disconnected {
    background: rgba(255, 23, 68, 0.1);
    color: var(--danger);
}

#app-status.disconnected::before {
    background-color: var(--danger);
    box-shadow: 0 0 8px var(--danger);
}

/* Accessibilité et taille de police */
html {
    font-size: calc(14px * var(--font-size-factor));
}

/* Mode contraste élevé */
body.high-contrast {
    --bg-dark: #000000;
    --bg-main: #121212;
    --bg-light: #1e1e1e;
    --bg-card: rgba(30, 30, 30, 0.9);
    --bg-card-hover: rgba(40, 40, 40, 0.95);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-muted: rgba(255, 255, 255, 0.7);
    --border-light: 1px solid rgba(255, 255, 255, 0.3);
    --border-accent: 1px solid rgba(255, 255, 255, 0.5);
    --primary: #4a90ff;
    --primary-light: #7ab8ff;
    --accent: #00ffff;
    --accent-glow: rgba(0, 255, 255, 0.6);
}

body.high-contrast .card,
body.high-contrast .sidebar,
body.high-contrast .header {
    border: 1px solid rgba(255, 255, 255, 0.3);
}

body.high-contrast .btn {
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* Mode couleurs inversées */
body.inverted-colors {
    filter: invert(1) hue-rotate(180deg);
}

body.inverted-colors img,
body.inverted-colors video {
    filter: invert(1) hue-rotate(180deg);
}

/* Espacement du texte augmenté */
body.increased-spacing {
    letter-spacing: 0.05em;
    word-spacing: 0.1em;
    line-height: 1.5;
}

body.increased-spacing p,
body.increased-spacing h1,
body.increased-spacing h2,
body.increased-spacing h3,
body.increased-spacing h4,
body.increased-spacing h5,
body.increased-spacing h6 {
    margin-bottom: 1.2em;
}

body.increased-spacing .btn,
body.increased-spacing .input,
body.increased-spacing .select {
    padding: 0.6em 1.2em;
}

/* Compatibilité lecteur d'écran */
body.screen-reader-compatible .visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

body.screen-reader-compatible :focus {
    outline: 3px solid var(--accent) !important;
    outline-offset: 2px !important;
}

body.screen-reader-compatible .skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--primary);
    color: white;
    padding: 8px;
    z-index: 9999;
}

body.screen-reader-compatible .skip-link:focus {
    top: 0;
}

/* Styles pour le générateur de contenu sans restrictions */
.generator-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    overflow: hidden;
    border: var(--border-light);
    box-shadow: var(--shadow);
}

.generator-header {
    padding: 15px 20px;
    background: rgba(10, 25, 41, 0.7);
    border-bottom: var(--border-light);
}

.generator-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.generator-title i {
    color: var(--accent);
    font-size: 1.4rem;
    text-shadow: 0 0 10px var(--accent-glow);
}

.generator-title h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
}

.generator-tabs {
    display: flex;
    background: rgba(15, 41, 66, 0.6);
    border-bottom: var(--border-light);
}

.generator-tab {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
}

.generator-tab:hover {
    background: rgba(30, 70, 110, 0.4);
    color: var(--text-primary);
}

.generator-tab.active {
    background: rgba(30, 70, 110, 0.6);
    border-bottom: 2px solid var(--accent);
    color: var(--accent);
}

.generator-tab i {
    font-size: 1.1rem;
}

.generator-content {
    padding: 20px;
}

.generator-section {
    display: none;
}

.generator-section.active {
    display: block;
}

.generator-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.prompt-input {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    background: rgba(10, 25, 41, 0.5);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-family: inherit;
    resize: vertical;
    transition: all var(--transition-fast);
}

.prompt-input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px var(--accent-glow);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    background: rgba(10, 25, 41, 0.5);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-family: inherit;
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px var(--accent-glow);
}

.generator-actions {
    display: flex;
    justify-content: flex-end;
}

.generator-disclaimer {
    padding: 10px 15px;
    background: rgba(255, 171, 0, 0.1);
    border-left: 3px solid var(--warning);
    border-radius: var(--border-radius-sm);
    margin-bottom: 20px;
}

.generator-disclaimer p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--warning);
    display: flex;
    align-items: center;
    gap: 8px;
}

.generator-results {
    background: rgba(15, 41, 66, 0.4);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    border: var(--border-light);
}

.generator-results-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.image-gallery img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
    border: var(--border-light);
    transition: all var(--transition-fast);
}

.image-gallery img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow);
    border-color: var(--accent);
}

.video-results {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.video-results video {
    width: 100%;
    border-radius: var(--border-radius-sm);
    border: var(--border-light);
}

.model3d-results {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.model3d-item {
    background: rgba(10, 25, 41, 0.5);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.model3d-preview {
    width: 100%;
    height: 200px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.model3d-actions {
    display: flex;
    justify-content: space-between;
}

/* Styles pour les indicateurs de chargement */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: var(--accent);
    font-size: 1.2rem;
    gap: 10px;
}

.loading-indicator i {
    font-size: 1.5rem;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.5;
        transform: scale(0.9);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
    100% {
        opacity: 0.5;
        transform: scale(0.9);
    }
}

/* Styles pour les conteneurs vidéo */
.video-container {
    background: rgba(10, 25, 41, 0.5);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.video-container video {
    width: 100%;
    display: block;
}

.video-info {
    padding: 15px;
}

.video-title {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.video-meta {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 10px;
}

.video-actions {
    display: flex;
    gap: 10px;
}

/* Styles pour les informations des modèles 3D */
.model3d-info {
    margin-bottom: 10px;
}

.model3d-title {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.model3d-meta {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Styles pour le laboratoire */
.code-lab-preview {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    margin: 15px 0;
    overflow: hidden;
}

.code-editor-preview {
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-code);
}

.code-editor-preview pre {
    margin: 0;
    overflow-x: auto;
}

/* Styles pour le générateur de contenu */
.generator-tabs {
    display: flex;
    border-bottom: var(--border-light);
    margin-bottom: 20px;
}

.generator-tab {
    padding: 10px 15px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.generator-tab:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.generator-tab.active {
    color: var(--accent);
    border-bottom: 2px solid var(--accent);
}

.generator-tab i {
    margin-right: 5px;
}

.generator-section {
    display: none;
}

.generator-section.active {
    display: block;
}

.generator-form {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-actions {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}

.generator-results {
    margin-top: 20px;
}

.generator-results h4 {
    margin-bottom: 15px;
    color: var(--text-primary);
    font-weight: 500;
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.image-gallery img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
    transition: transform 0.3s ease;
}

.image-gallery img:hover {
    transform: scale(1.05);
}

.video-results {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.model3d-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.model3d-item {
    background: rgba(10, 25, 41, 0.5);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    padding: 15px;
}

.model3d-preview {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-sm);
    margin-bottom: 15px;
    color: var(--accent);
}

.model3d-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.placeholder-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
}

.placeholder-message i {
    font-size: 2rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

/* Styles pour les métriques de performance */
.performance-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.metric-card {
    flex: 1;
    background: rgba(10, 25, 41, 0.5);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    text-align: center;
    margin: 0 5px;
}

.metric-icon {
    font-size: 1.5rem;
    color: var(--accent);
    margin-bottom: 10px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Styles pour les expériences */
.experiments-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.experiment-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(10, 25, 41, 0.3);
    border-radius: var(--border-radius-sm);
    padding: 15px;
}

.experiment-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.experiment-status.running {
    background: var(--accent);
    box-shadow: 0 0 10px var(--accent);
    animation: pulse 1.5s infinite;
}

.experiment-status.pending {
    background: var(--warning);
}

.experiment-status.completed {
    background: var(--success);
}

.experiment-info {
    flex: 1;
}

.experiment-name {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.experiment-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--accent);
    border-radius: 3px;
}

.progress-value {
    font-size: 0.85rem;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: right;
}

/* Styles pour les expériences en cours */
.experiments-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.experiment-item {
    background: rgba(15, 41, 66, 0.4);
    border: var(--border-light);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    transition: all var(--transition-fast);
}

.experiment-item:hover {
    background: rgba(30, 70, 110, 0.4);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.experiment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.experiment-title {
    font-weight: 500;
    color: var(--text-primary);
}

.experiment-status {
    font-size: 0.8rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.experiment-status.running {
    background: rgba(0, 176, 255, 0.1);
    color: var(--info);
}

.experiment-status.completed {
    background: rgba(0, 230, 118, 0.1);
    color: var(--success);
}

.experiment-status.planned {
    background: rgba(255, 171, 0, 0.1);
    color: var(--warning);
}

.experiment-progress {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--info);
    border-radius: 3px;
}

.experiment-status.completed .progress-fill {
    background: var(--success);
}

.progress-value {
    font-size: 0.9rem;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: right;
}

.experiment-details {
    display: flex;
    gap: 15px;
}

.experiment-metric {
    flex: 1;
}

.metric-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.metric-value {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-primary);
}

.metric-value.success {
    color: var(--success);
}

.mt-md {
    margin-top: 20px;
}

/* Adaptations responsives */
@media (max-width: 1400px) {
    .grid-4 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) {
    .grid-3, .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }

    .sidebar {
        width: 250px;
        left: -250px;
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar.active {
        left: 0;
    }

    .header-title {
        font-size: 1.2rem;
    }
}

/* Styles pour les niveaux de mémoire */
.memory-levels {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.memory-level {
    display: flex;
    align-items: center;
    gap: 10px;
}

.memory-level-name {
    width: 100px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.memory-level-bar {
    flex: 1;
    height: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.memory-level-fill {
    height: 100%;
    background: var(--accent);
    border-radius: 4px;
    width: 0;
    transition: width 1s ease;
}

.memory-level-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--accent);
}

/* Styles pour les contrôles du gardien de mémoire */
.memory-guardian-controls {
    background: rgba(10, 25, 41, 0.3);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    margin-top: 15px;
}

.guardian-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-label {
    color: var(--text-secondary);
}

.status-value {
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
}

.status-value.active {
    background: rgba(0, 255, 0, 0.15);
    color: #4caf50;
}

.status-value.inactive {
    background: rgba(255, 0, 0, 0.15);
    color: #f44336;
}

.memory-test-controls {
    margin-top: 15px;
}

.memory-test-results {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-sm);
    padding: 12px;
    margin-top: 10px;
}

.memory-test-results.hidden {
    display: none;
}

.test-status {
    margin-bottom: 10px;
    color: var(--text-primary);
}

.test-progress {
    margin-top: 8px;
}

.test-details {
    margin-top: 15px;
    background: rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius-sm);
    padding: 10px;
}

.test-detail-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.test-detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--text-secondary);
}

.detail-value {
    font-weight: 500;
    color: var(--text-primary);
}

.mt-2 {
    margin-top: 10px;
}

.mt-3 {
    margin-top: 15px;
}

/* Styles pour la zone de saisie de message en bas de la fenêtre */
.chat-input-container {
    position: relative;
    margin-top: 20px;
    background: rgba(10, 25, 41, 0.7);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-input-toolbar {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.toolbar-section {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.chat-input-main {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.1);
}

.input-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-action {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.input-action:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.chat-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: rgba(0, 0, 0, 0.15);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    font-size: 0.85rem;
}

.input-status {
    color: var(--text-secondary);
    font-style: italic;
}

.input-options {
    position: relative;
}

.option-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
}

.option-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.option-dropdown {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 10px;
    min-width: 200px;
    z-index: 100;
    margin-bottom: 10px;
    display: none;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 5px;
    cursor: pointer;
    border-radius: 4px;
}

.option-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.option-btn:focus + .option-dropdown,
.option-dropdown:hover {
    display: block;
}

/* Styles pour la zone de texte */
textarea.input {
    flex: 1;
    min-height: 60px;
    max-height: 150px;
    resize: vertical;
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 1rem;
    line-height: 1.5;
    transition: all 0.2s ease;
}

textarea.input:focus {
    outline: none;
    border-color: var(--accent);
    background: rgba(0, 0, 0, 0.3);
}

/* Styles pour la section de conversation */
.chat-interface {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    min-height: 500px;
}

.chat-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0 15px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px 0;
}

.chat-sidebar {
    width: 300px;
    border-left: var(--border-light);
    background: rgba(10, 25, 41, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
