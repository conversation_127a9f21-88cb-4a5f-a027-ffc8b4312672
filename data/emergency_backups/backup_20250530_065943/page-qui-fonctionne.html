<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Louna AI v2.1.0 - Hub Central IA Avancée (100% Complet)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <!-- Scripts essentiels -->
    <script src="/js/global-config.js"></script>
    <script src="/js/louna-notifications.js"></script>
    <style>
        /* ===== STYLES DE BASE ===== */
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* ===== BANNIÈRES DE MODE ===== */
        .hibernation-mode-banner {
            background: linear-gradient(135deg, #673ab7, #512da8);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-weight: 600;
            animation: pulse 2s infinite;
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(103, 58, 183, 0.4);
        }

        .hibernation-mode-banner.active { display: block; }

        .sleep-mode-banner {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-weight: 600;
            animation: pulse 2s infinite;
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .sleep-mode-banner.active { display: block; }

        /* ===== CONTRÔLES DE SÉCURITÉ ===== */
        .security-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1001;
            flex-wrap: wrap;
        }

        .security-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .security-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .btn-home { border-color: #4caf50; color: #4caf50; }
        .btn-hibernation { border-color: #9c27b0; color: #9c27b0; }
        .btn-sleep { border-color: #2196f3; color: #2196f3; }
        .btn-wakeup { border-color: #ff9800; color: #ff9800; }
        .btn-surveillance { border-color: #f44336; color: #f44336; }
        .btn-backup { border-color: #00bcd4; color: #00bcd4; }
        .btn-memory { border-color: #ff69b4; color: #ff69b4; }

        .btn-hibernation.active, .btn-sleep.active {
            background: rgba(156, 39, 176, 0.3);
            animation: pulseViolet 2s infinite;
        }

        .btn-wakeup.flash {
            animation: flashOrange 1s infinite;
        }

        /* ===== INDICATEUR D'ÉTAT GLOBAL ===== */
        .global-status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(76, 175, 80, 0.5);
            border-radius: 15px;
            padding: 15px 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .status-icon {
            font-size: 24px;
            color: #4caf50;
            animation: pulse 2s infinite;
        }

        .status-text {
            color: #4caf50;
            font-weight: bold;
            font-size: 16px;
        }

        /* ===== HEADER PRINCIPAL ===== */
        .header {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
            position: relative;
            overflow: hidden;
            margin-top: 80px;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        .header-subtitle {
            font-size: 1.3rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .qi-display {
            background: rgba(0, 0, 0, 0.4);
            display: inline-block;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.8rem;
            font-weight: bold;
            margin-top: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: pulseGlow 3s ease-in-out infinite;
        }

        /* ===== STATISTIQUES TEMPS RÉEL ===== */
        .stats-container {
            max-width: 1400px;
            margin: 40px auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px solid rgba(255, 105, 180, 0.3);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(233, 30, 99, 0.3);
            border-color: #ff69b4;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 10px;
            text-shadow: 0 0 15px rgba(255, 105, 180, 0.5);
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* ===== SECTION DÉMARRAGE RAPIDE ===== */
        .quick-start-section {
            max-width: 1400px;
            margin: 50px auto;
            padding: 0 20px;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #ff69b4;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .quick-start-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 50px;
        }

        .quick-start-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 105, 180, 0.3);
            position: relative;
            overflow: hidden;
        }

        .quick-start-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }

        .quick-start-card:hover::before {
            opacity: 1;
            animation: shine 0.5s ease-in-out;
        }

        .quick-start-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(233, 30, 99, 0.3);
            border-color: #ff69b4;
        }

        .quick-start-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ff69b4;
            text-shadow: 0 0 15px rgba(255, 105, 180, 0.5);
        }

        .quick-start-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #ff69b4;
        }

        .quick-start-desc {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.85rem;
            line-height: 1.4;
        }

        /* ===== CARTE TÉLÉPHONE PRIORITAIRE ===== */
        .phone-priority {
            border: 3px solid #00ff00 !important;
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.1)) !important;
            animation: pulseGreen 2s infinite;
        }

        .phone-priority .quick-start-icon {
            color: #00ff00 !important;
            animation: bounce 2s infinite;
        }

        .phone-priority .quick-start-title {
            color: #00ff00 !important;
        }
    </style>
</head>
<body>
    <!-- Bannière Mode Hibernation Profonde -->
    <div class="hibernation-mode-banner" id="hibernationBanner">
        <i class="fas fa-snowflake hibernation-icon"></i>
        <strong>MODE HIBERNATION PROFONDE ACTIVÉ</strong> - L'agent est en hibernation très profonde.
        <br>
        <strong>🔓 POUR RÉVEILLER :</strong> Cliquez sur le bouton "Réveil" ☀️ en haut à droite (Code 2338 automatique pour Jean-Luc)
    </div>

    <!-- Bannière Mode Sommeil -->
    <div class="sleep-mode-banner" id="sleepBanner">
        <i class="fas fa-moon sleep-icon"></i>
        <strong>MODE SOMMEIL ACTIVÉ</strong> - L'agent est en veille sécurisée. Utilisez le code 2338 pour le réveiller.
    </div>

    <!-- Indicateur d'État Global -->
    <div class="global-status-indicator" id="globalStatus">
        <div class="status-icon">
            <i class="fas fa-brain"></i>
        </div>
        <div class="status-text" id="statusText">
            AGENT ÉVEILLÉ
        </div>
    </div>

    <!-- Contrôles de Sécurité -->
    <div class="security-controls">
        <a href="/" class="security-btn btn-home" title="Retour à l'accueil">
            <i class="fas fa-home"></i>
            <span>Accueil</span>
        </a>
        <button class="security-btn btn-hibernation" onclick="activateHibernation()" title="Mode hibernation profonde (Code: 2338)">
            <i class="fas fa-snowflake"></i>
            <span>Hibernation</span>
        </button>
        <button class="security-btn btn-sleep" onclick="activateSleep()" title="Mode sommeil sécurisé (Code: 2338)">
            <i class="fas fa-moon"></i>
            <span>Sommeil</span>
        </button>
        <button class="security-btn btn-wakeup" onclick="wakeupAgent()" title="Réveiller l'agent (Code: 2338)">
            <i class="fas fa-sun"></i>
            <span>Réveil</span>
        </button>
        <button class="security-btn btn-surveillance" onclick="openSurveillance()" title="Surveillance sécurité">
            <i class="fas fa-shield-alt"></i>
            <span>Surveillance</span>
        </button>
        <button class="security-btn btn-backup" onclick="openBackup()" title="Gestionnaire de sauvegarde">
            <i class="fas fa-save"></i>
            <span>Sauvegarde</span>
        </button>
        <button class="security-btn btn-memory" onclick="openMemoryControl()" title="Contrôle mémoire thermique">
            <i class="fas fa-brain"></i>
            <span>Mémoire</span>
        </button>
    </div>

    <!-- Header Principal -->
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-brain"></i> Louna AI v2.1.0</h1>
            <p class="header-subtitle">Intelligence Artificielle Évolutive - Hub Central</p>
            <div class="qi-display">
                <i class="fas fa-bolt"></i> QI: 225
            </div>
        </div>
    </div>

    <!-- Statistiques Temps Réel -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-value" id="qi-value">225</div>
            <div class="stat-label">QI Système (Quasi-AGI)</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="memory-temp">37.0°C</div>
            <div class="stat-label">Mémoire Thermique</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="neurons-active">89</div>
            <div class="stat-label">Neurones Actifs</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="kyber-accelerators">8/16</div>
            <div class="stat-label">Accélérateurs KYBER</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="completion">100%</div>
            <div class="stat-label">Fonctionnalités Complètes</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="learning-rate">94.2%</div>
            <div class="stat-label">Taux d'Apprentissage</div>
        </div>
    </div>

    <!-- Section Démarrage Rapide -->
    <div class="quick-start-section">
        <h2 class="section-title">🚀 Démarrage Rapide</h2>
        <div class="quick-start-grid">
            <!-- Connexion Téléphone - PRIORITÉ ABSOLUE -->
            <div class="quick-start-card phone-priority" onclick="openApp('/phone-camera-system.html')">
                <div class="quick-start-icon"><i class="fas fa-mobile-alt"></i></div>
                <div class="quick-start-title">📱 Téléphone</div>
                <div class="quick-start-desc">Connexion Wi-Fi</div>
            </div>

            <!-- Chat IA -->
            <div class="quick-start-card" onclick="openApp('/chat-agents.html')">
                <div class="quick-start-icon"><i class="fas fa-comments"></i></div>
                <div class="quick-start-title">💬 Chat</div>
                <div class="quick-start-desc">IA Conversation</div>
            </div>

            <!-- Génération -->
            <div class="quick-start-card" onclick="openApp('/generation-center.html')">
                <div class="quick-start-icon"><i class="fas fa-magic"></i></div>
                <div class="quick-start-title">🎨 Génération</div>
                <div class="quick-start-desc">Images, Vidéos, Musique</div>
            </div>

            <!-- Mémoire -->
            <div class="quick-start-card" onclick="openApp('/futuristic-interface.html')">
                <div class="quick-start-icon"><i class="fas fa-fire"></i></div>
                <div class="quick-start-title">🧠 Mémoire</div>
                <div class="quick-start-desc">Système Thermique</div>
            </div>

            <!-- Cerveau -->
            <div class="quick-start-card" onclick="openApp('/brain-dashboard-live.html')">
                <div class="quick-start-icon"><i class="fas fa-brain"></i></div>
                <div class="quick-start-title">📊 Cerveau</div>
                <div class="quick-start-desc">Monitoring Live</div>
            </div>

            <!-- KYBER -->
            <div class="quick-start-card" onclick="openApp('/kyber-dashboard.html')">
                <div class="quick-start-icon"><i class="fas fa-bolt"></i></div>
                <div class="quick-start-title">⚡ KYBER</div>
                <div class="quick-start-desc">Accélérateurs</div>
            </div>

            <!-- Docs -->
            <div class="quick-start-card" onclick="openApp('/presentation-complete')">
                <div class="quick-start-icon"><i class="fas fa-book"></i></div>
                <div class="quick-start-title">📚 Docs</div>
                <div class="quick-start-desc">Documentation</div>
            </div>
        </div>
    </div>

    <!-- Applications Principales -->
    <div class="quick-start-section">
        <h2 class="section-title">🎯 Applications Principales</h2>
        <div class="quick-start-grid">
            <!-- Chat Intelligent Avancé -->
            <div class="quick-start-card" onclick="openApp('/chat-agents.html')">
                <div class="quick-start-icon"><i class="fas fa-comments"></i></div>
                <div class="quick-start-title">🧠 Chat Intelligent Avancé</div>
                <div class="quick-start-desc">Conversation IA avec QI 225</div>
            </div>

            <!-- Centre de Génération IA -->
            <div class="quick-start-card" onclick="openApp('/generation-center.html')">
                <div class="quick-start-icon"><i class="fas fa-magic"></i></div>
                <div class="quick-start-title">🎨 Centre de Génération IA</div>
                <div class="quick-start-desc">Images, Vidéos, Musique, 3D</div>
            </div>

            <!-- Génération d'Images -->
            <div class="quick-start-card" onclick="openApp('/image-generation.html')">
                <div class="quick-start-icon"><i class="fas fa-image"></i></div>
                <div class="quick-start-title">🖼️ Génération d'Images</div>
                <div class="quick-start-desc">Création d'images IA</div>
            </div>

            <!-- Génération Vidéo LTX -->
            <div class="quick-start-card" onclick="openApp('/video-generation.html')">
                <div class="quick-start-icon"><i class="fas fa-video"></i></div>
                <div class="quick-start-title">🎥 Génération Vidéo LTX</div>
                <div class="quick-start-desc">Vidéos IA haute qualité</div>
            </div>

            <!-- Laboratoire YouTube -->
            <div class="quick-start-card" onclick="openApp("/youtube-laboratory.html")">
                <div class="quick-start-icon"><i class="fab fa-youtube"></i></div>
                <div class="quick-start-title">🧪 Laboratoire d'Analyses YouTube</div>
                <div class="quick-start-desc">Observez en temps réel ce que Louna voit, comprend et apprend</div>
            </div>
            <!-- Centre de Sécurité -->
            <div class="quick-start-card" onclick="openApp('/security-center.html')">
                <div class="quick-start-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="quick-start-title">🔐 Centre de Sécurité Avancée</div>
                <div class="quick-start-desc">Protection complète</div>
            </div>

            <!-- Générateur Musical -->
            <div class="quick-start-card" onclick="openApp('/music-generation.html')">
                <div class="quick-start-icon"><i class="fas fa-music"></i></div>
                <div class="quick-start-title">🎵 Générateur Musical IA</div>
                <div class="quick-start-desc">Composition musicale</div>
            </div>

            <!-- Contrôle d'Urgence -->
            <div class="quick-start-card" onclick="openApp('/emergency-control.html')" style="border-color: #f44336;">
                <div class="quick-start-icon" style="color: #f44336;"><i class="fas fa-exclamation-triangle"></i></div>
                <div class="quick-start-title" style="color: #f44336;">🚨 CONTRÔLE D'URGENCE</div>
                <div class="quick-start-desc">Arrêt d'urgence système</div>
            </div>

            <!-- Sauvegarde Système -->
            <div class="quick-start-card" onclick="openApp('/backup-system.html')" style="border-color: #4caf50;">
                <div class="quick-start-icon" style="color: #4caf50;"><i class="fas fa-save"></i></div>
                <div class="quick-start-title" style="color: #4caf50;">💾 SAUVEGARDE SYSTÈME</div>
                <div class="quick-start-desc">Backup automatique</div>
            </div>

            <!-- Présentation Complète -->
            <div class="quick-start-card" onclick="openApp('/presentation-complete')" style="border-color: #ff69b4;">
                <div class="quick-start-icon" style="color: #ff69b4;"><i class="fas fa-presentation"></i></div>
                <div class="quick-start-title" style="color: #ff69b4;">📚 PRÉSENTATION COMPLÈTE</div>
                <div class="quick-start-desc">Guide complet Louna</div>
            </div>

            <!-- Recherche Web IA -->
            <div class="quick-start-card" onclick="openApp('/web-search.html')">
                <div class="quick-start-icon"><i class="fas fa-search"></i></div>
                <div class="quick-start-title">🔍 Recherche Web IA</div>
                <div class="quick-start-desc">Recherche intelligente</div>
            </div>

            <!-- Navigateur Web -->
            <div class="quick-start-card" onclick="openApp('/web-browser.html')">
                <div class="quick-start-icon"><i class="fas fa-globe"></i></div>
                <div class="quick-start-title">🌐 Navigateur Web Intégré</div>
                <div class="quick-start-desc">Navigation web</div>
            </div>

            <!-- Reconnaissance Faciale -->
            <div class="quick-start-card" onclick="openApp('/face-recognition.html')">
                <div class="quick-start-icon"><i class="fas fa-user-check"></i></div>
                <div class="quick-start-title">👁️ Reconnaissance Faciale</div>
                <div class="quick-start-desc">Analyse faciale IA</div>
            </div>

            <!-- Système Vocal Féminine -->
            <div class="quick-start-card" onclick="openApp('/voice-system-enhanced.html')" style="border-color: #ff69b4;">
                <div class="quick-start-icon" style="color: #ff69b4;"><i class="fas fa-microphone-alt"></i></div>
                <div class="quick-start-title" style="color: #ff69b4;">🎤 SYSTÈME VOCAL FÉMININE</div>
                <div class="quick-start-desc">Voix IA perfectionnée</div>
            </div>

            <!-- Apprentissage Vocal YouTube -->
            <div class="quick-start-card" onclick="openApp('/voice-learning-interface.html')" style="border-color: #feca57;">
                <div class="quick-start-icon" style="color: #feca57;"><i class="fas fa-graduation-cap"></i></div>
                <div class="quick-start-title" style="color: #feca57;">🎓 APPRENTISSAGE VOCAL YOUTUBE</div>
                <div class="quick-start-desc">Voix naturelle depuis YouTube</div>
            </div>

            <!-- Caméra/Micro Téléphone -->
            <div class="quick-start-card phone-priority" onclick="openApp('/phone-camera-system.html')">
                <div class="quick-start-icon"><i class="fas fa-mobile-alt"></i></div>
                <div class="quick-start-title">📱 CAMÉRA/MICRO TÉLÉPHONE</div>
                <div class="quick-start-desc">Connexion Wi-Fi</div>
            </div>

            <!-- Éditeur Code Avancé -->
            <div class="quick-start-card" onclick="openApp('/advanced-code-editor.html')" style="border-color: #9c27b0;">
                <div class="quick-start-icon" style="color: #9c27b0;"><i class="fas fa-code"></i></div>
                <div class="quick-start-title" style="color: #9c27b0;">💻 ÉDITEUR CODE AVANCÉ</div>
                <div class="quick-start-desc">IDE professionnel</div>
            </div>
        </div>
    </div>

    <!-- Applications Avancées -->
    <div class="quick-start-section">
        <h2 class="section-title">🚀 Applications Avancées</h2>
        <div class="quick-start-grid">
            <!-- Dashboard Apprentissage -->
            <div class="quick-start-card" onclick="openApp('/learning-dashboard.html')">
                <div class="quick-start-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="quick-start-title">📊 Dashboard Apprentissage</div>
                <div class="quick-start-desc">Suivi évolution IA</div>
            </div>

            <!-- Test Mémoire Biologique -->
            <div class="quick-start-card" onclick="openApp('/memory-test.html')">
                <div class="quick-start-icon"><i class="fas fa-brain"></i></div>
                <div class="quick-start-title">🧬 Test Mémoire Biologique</div>
                <div class="quick-start-desc">Analyse mémoire</div>
            </div>

            <!-- Centre de Diagnostic Agent -->
            <div class="quick-start-card" onclick="openApp('/qi-test-simple.html')" style="border-color: #ff69b4;">
                <div class="quick-start-icon" style="color: #ff69b4;"><i class="fas fa-stethoscope"></i></div>
                <div class="quick-start-title" style="color: #ff69b4;">🩺 CENTRE DE DIAGNOSTIC AGENT</div>
                <div class="quick-start-desc">Tests QI et performance complète</div>
            </div>

            <!-- Test Évolution QI -->
            <div class="quick-start-card" onclick="openApp('/qi-evolution-test.html')" style="border-color: #4caf50;">
                <div class="quick-start-icon" style="color: #4caf50;"><i class="fas fa-chart-line"></i></div>
                <div class="quick-start-title" style="color: #4caf50;">📈 TEST ÉVOLUTION QI</div>
                <div class="quick-start-desc">Questions sur l'évolution intellectuelle</div>
            </div>

            <!-- Configuration Claude -->
            <div class="quick-start-card" onclick="openApp('/claude-setup-guide.html')" style="border-color: #9c27b0;">
                <div class="quick-start-icon" style="color: #9c27b0;"><i class="fas fa-robot"></i></div>
                <div class="quick-start-title" style="color: #9c27b0;">🤖 CONFIGURATION CLAUDE</div>
                <div class="quick-start-desc">Guide connexion agent réel</div>
            </div>

            <!-- Fonctionnalités Avancées -->
            <div class="quick-start-card" onclick="openApp('/advanced-features.html')">
                <div class="quick-start-icon"><i class="fas fa-cogs"></i></div>
                <div class="quick-start-title">🚀 Fonctionnalités Avancées</div>
                <div class="quick-start-desc">Outils experts</div>
            </div>

            <!-- Connexion Directe -->
            <div class="quick-start-card" onclick="openApp('/direct-connection.html')">
                <div class="quick-start-icon"><i class="fas fa-link"></i></div>
                <div class="quick-start-title">⚡ Connexion Directe</div>
                <div class="quick-start-desc">Liaison directe IA</div>
            </div>

            <!-- Mémoire Thermique -->
            <div class="quick-start-card" onclick="openApp('/futuristic-interface.html')">
                <div class="quick-start-icon"><i class="fas fa-fire"></i></div>
                <div class="quick-start-title">🔥 Mémoire Thermique</div>
                <div class="quick-start-desc">Interface futuriste</div>
            </div>

            <!-- Tableau de Bord VIVANT -->
            <div class="quick-start-card" onclick="openApp('/brain-dashboard-live.html')">
                <div class="quick-start-icon"><i class="fas fa-heartbeat"></i></div>
                <div class="quick-start-title">🧠 Tableau de Bord VIVANT</div>
                <div class="quick-start-desc">Monitoring temps réel</div>
            </div>

            <!-- Cerveau 3D VIVANT -->
            <div class="quick-start-card" onclick="openApp('/brain-3d-live.html')">
                <div class="quick-start-icon"><i class="fas fa-cube"></i></div>
                <div class="quick-start-title">🧠 Cerveau 3D VIVANT</div>
                <div class="quick-start-desc">Visualisation 3D</div>
            </div>

            <!-- Visualisation Cerveau 3D -->
            <div class="quick-start-card" onclick="openApp('/brain-visualization-3d.html')">
                <div class="quick-start-icon"><i class="fas fa-eye"></i></div>
                <div class="quick-start-title">🧠 Visualisation Cerveau 3D</div>
                <div class="quick-start-desc">Analyse visuelle</div>
            </div>

            <!-- Monitoring QI & Neurones -->
            <div class="quick-start-card" onclick="openApp('/qi-neuron-monitoring.html')">
                <div class="quick-start-icon"><i class="fas fa-chart-line"></i></div>
                <div class="quick-start-title">⚖️ Monitoring QI & Neurones</div>
                <div class="quick-start-desc">Suivi performance</div>
            </div>

            <!-- Monitoring Cérébral Complet -->
            <div class="quick-start-card" onclick="openApp('/brain-monitoring-complete.html')">
                <div class="quick-start-icon"><i class="fas fa-brain"></i></div>
                <div class="quick-start-title">🧠 Monitoring Cérébral Complet</div>
                <div class="quick-start-desc">Analyse complète</div>
            </div>

            <!-- Persistance Mémoire Thermique -->
            <div class="quick-start-card" onclick="openApp('/thermal-memory-persistence.html')">
                <div class="quick-start-icon"><i class="fas fa-database"></i></div>
                <div class="quick-start-title">💾 Persistance Mémoire Thermique</div>
                <div class="quick-start-desc">Sauvegarde mémoire</div>
            </div>

            <!-- Transfert d'Informations -->
            <div class="quick-start-card" onclick="openApp('/information-transfer.html')">
                <div class="quick-start-icon"><i class="fas fa-exchange-alt"></i></div>
                <div class="quick-start-title">🔄 Transfert d'Informations</div>
                <div class="quick-start-desc">Échange de données</div>
            </div>

            <!-- Centre d'Évolution & Apprentissage -->
            <div class="quick-start-card" onclick="openApp('/evolution-learning-center.html')">
                <div class="quick-start-icon"><i class="fas fa-dna"></i></div>
                <div class="quick-start-title">🧬 Centre d'Évolution & Apprentissage</div>
                <div class="quick-start-desc">Évolution IA</div>
            </div>

            <!-- Accélérateurs Kyber -->
            <div class="quick-start-card" onclick="openApp('/kyber-dashboard.html')">
                <div class="quick-start-icon"><i class="fas fa-rocket"></i></div>
                <div class="quick-start-title">⚡ Accélérateurs Kyber</div>
                <div class="quick-start-desc">Boost performance</div>
            </div>

            <!-- Studio de Génération -->
            <div class="quick-start-card" onclick="openApp('/generation-studio.html')">
                <div class="quick-start-icon"><i class="fas fa-palette"></i></div>
                <div class="quick-start-title">🎨 Studio de Génération</div>
                <div class="quick-start-desc">Création avancée</div>
            </div>

            <!-- Interface Vocale -->
            <div class="quick-start-card" onclick="openApp('/voice-interface.html')">
                <div class="quick-start-icon"><i class="fas fa-microphone"></i></div>
                <div class="quick-start-title">🎤 Interface Vocale</div>
                <div class="quick-start-desc">Commandes vocales</div>
            </div>

            <!-- Paramètres Avancés -->
            <div class="quick-start-card" onclick="openApp('/advanced-settings.html')">
                <div class="quick-start-icon"><i class="fas fa-sliders-h"></i></div>
                <div class="quick-start-title">⚙️ Paramètres Avancés</div>
                <div class="quick-start-desc">Configuration système</div>
            </div>
        </div>
    </div>

    <!-- Applications Système -->
    <div class="quick-start-section">
        <h2 class="section-title">⚙️ Applications Système</h2>
        <div class="quick-start-grid">
            <!-- Dashboard de Contrôle -->
            <div class="quick-start-card" onclick="openApp('/control-dashboard.html')">
                <div class="quick-start-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="quick-start-title">🎛️ Dashboard de Contrôle</div>
                <div class="quick-start-desc">Contrôle système</div>
            </div>

            <!-- Tableau de Bord Principal -->
            <div class="quick-start-card" onclick="openApp('/main-dashboard.html')">
                <div class="quick-start-icon"><i class="fas fa-chart-pie"></i></div>
                <div class="quick-start-title">📊 Tableau de Bord Principal</div>
                <div class="quick-start-desc">Vue d'ensemble</div>
            </div>

            <!-- Éditeur de Code -->
            <div class="quick-start-card" onclick="openApp('/code-editor.html')">
                <div class="quick-start-icon"><i class="fas fa-code"></i></div>
                <div class="quick-start-title">💻 Éditeur de Code</div>
                <div class="quick-start-desc">Développement</div>
            </div>

            <!-- Logs Système -->
            <div class="quick-start-card" onclick="openApp('/system-logs.html')">
                <div class="quick-start-icon"><i class="fas fa-list-alt"></i></div>
                <div class="quick-start-title">📋 Logs Système</div>
                <div class="quick-start-desc">Journaux système</div>
            </div>

            <!-- Analyses Comparatives -->
            <div class="quick-start-card" onclick="openApp('/comparative-analysis.html')">
                <div class="quick-start-icon"><i class="fas fa-balance-scale"></i></div>
                <div class="quick-start-title">⚖️ Analyses Comparatives</div>
                <div class="quick-start-desc">Comparaisons</div>
            </div>

            <!-- Gestionnaire QI -->
            <div class="quick-start-card" onclick="openApp('/qi-manager.html')">
                <div class="quick-start-icon"><i class="fas fa-brain"></i></div>
                <div class="quick-start-title">🧠 Gestionnaire QI</div>
                <div class="quick-start-desc">Gestion intelligence</div>
            </div>

            <!-- Test Mémoire Thermique -->
            <div class="quick-start-card" onclick="openApp('/thermal-memory-test.html')">
                <div class="quick-start-icon"><i class="fas fa-thermometer-half"></i></div>
                <div class="quick-start-title">🔬 Test Mémoire Thermique</div>
                <div class="quick-start-desc">Tests mémoire</div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="quick-start-section" style="text-align: center; padding: 40px 20px; background: rgba(0, 0, 0, 0.3); margin-top: 50px;">
        <h3 style="color: #ff69b4; margin-bottom: 20px;">🧠 Louna AI v2.1.0 - Intelligence Artificielle Évolutive</h3>
        <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 10px;">
            Créé par <strong>Jean-Luc Passave</strong> - Sainte-Anne, Guadeloupe 🏝️
        </p>
        <p style="color: #ff69b4; font-weight: bold;">
            <i class="fas fa-heart"></i> Système 100% Fonctionnel avec QI 225
        </p>
        <p style="color: rgba(255, 255, 255, 0.6); margin-top: 15px; font-size: 0.9rem;">
            🔥 Mémoire Thermique • ⚡ Accélérateurs KYBER • 📱 Connexion Téléphone • 🎨 Génération IA • 🔐 Sécurité Avancée
        </p>
    </div>

    <!-- Modal de Réveil avec Saisie de Code -->
    <div class="modal-overlay" id="wakeupModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.8); z-index: 10000; align-items: center; justify-content: center;">
        <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e, #16213e); border-radius: 20px; padding: 30px; max-width: 500px; width: 90%; border: 2px solid #ff69b4;">
            <div class="modal-header" style="text-align: center; margin-bottom: 25px;">
                <h2 style="color: #ff69b4; margin-bottom: 10px;">
                    <i class="fas fa-sun"></i>
                    Réveil de l'Agent Louna
                </h2>
                <button class="modal-close" onclick="closeWakeupModal()" style="position: absolute; top: 15px; right: 15px; background: none; border: none; color: #ff69b4; font-size: 24px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="security-warning" style="background: rgba(255, 152, 0, 0.1); border: 2px solid rgba(255, 152, 0, 0.3); border-radius: 10px; padding: 20px; margin-bottom: 25px; text-align: center;">
                    <i class="fas fa-shield-alt" style="color: #ff9800; font-size: 24px; margin-bottom: 10px;"></i>
                    <p style="color: white; margin-bottom: 10px;">L'agent est actuellement en <strong id="currentSleepMode">mode sommeil</strong>.</p>
                    <p style="color: white;">Veuillez saisir le code de sécurité pour le réveiller :</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="color: #ff69b4; display: block; margin-bottom: 8px; font-weight: bold;">Code de Sécurité :</label>
                    <input type="password" id="securityCodeInput" placeholder="Entrez le code (2338)" style="width: 100%; padding: 15px; border: 2px solid #ff69b4; border-radius: 10px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 16px;">
                </div>

                <div style="margin-bottom: 25px;">
                    <label style="color: #ff69b4; display: block; margin-bottom: 8px; font-weight: bold;">Authentification Utilisateur :</label>
                    <input type="text" id="userAuthInput" placeholder="Jean-Luc (optionnel)" style="width: 100%; padding: 15px; border: 2px solid #ff69b4; border-radius: 10px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 16px;">
                </div>

                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button onclick="confirmWakeup()" style="background: linear-gradient(135deg, #4caf50, #2e7d32); border: none; color: white; padding: 15px 30px; border-radius: 10px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-sun"></i> Réveiller
                    </button>
                    <button onclick="closeWakeupModal()" style="background: linear-gradient(135deg, #f44336, #d32f2f); border: none; color: white; padding: 15px 30px; border-radius: 10px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ===== VARIABLES GLOBALES =====
        let isInSleepMode = false;
        let isInDeepHibernation = false;

        // ===== FONCTIONS DE NAVIGATION =====
        function openApp(url) {
            console.log('🚀 Ouverture de:', url);
            window.location.href = url;
        }

        // ===== FONCTIONS DE CONTRÔLE DE SÉCURITÉ =====

        // Activer le mode hibernation profonde
        async function activateHibernation() {
            console.log('❄️ Activation hibernation profonde...');

            if (isInDeepHibernation) {
                showWarning('L\'agent est déjà en hibernation profonde');
                return;
            }

            try {
                const response = await fetch('/api/security/hibernation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'activate', code: '2338' })
                });

                if (response.ok) {
                    isInDeepHibernation = true;
                    isInSleepMode = false;

                    // Afficher la bannière d'hibernation
                    document.getElementById('hibernationBanner').classList.add('active');
                    document.getElementById('sleepBanner').classList.remove('active');

                    // Mettre à jour les boutons
                    document.querySelector('.btn-hibernation').classList.add('active');
                    document.querySelector('.btn-sleep').classList.remove('active');

                    // Désactiver toutes les fonctionnalités
                    disableAllFeatures();

                    // Mettre à jour l'état visuel
                    updateVisualState('hibernating');

                    showSuccess('🥶 Mode hibernation profonde activé - Agent en hibernation très profonde');
                    console.log('❄️ Hibernation profonde activée avec succès');
                } else {
                    throw new Error('Erreur serveur hibernation');
                }
            } catch (error) {
                console.error('Erreur hibernation:', error);
                showError('Erreur lors de l\'activation de l\'hibernation');
            }
        }

        // Activer le mode sommeil
        async function activateSleep() {
            console.log('😴 Activation mode sommeil...');

            if (isInSleepMode) {
                showWarning('L\'agent est déjà en mode sommeil');
                return;
            }

            try {
                const response = await fetch('/api/security/sleep', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'activate', code: '2338' })
                });

                if (response.ok) {
                    isInSleepMode = true;
                    isInDeepHibernation = false;

                    // Afficher la bannière de sommeil
                    document.getElementById('sleepBanner').classList.add('active');
                    document.getElementById('hibernationBanner').classList.remove('active');

                    // Mettre à jour les boutons
                    document.querySelector('.btn-sleep').classList.add('active');
                    document.querySelector('.btn-hibernation').classList.remove('active');

                    // Désactiver les fonctionnalités non essentielles
                    disableNonEssentialFeatures();

                    // Mettre à jour l'état visuel
                    updateVisualState('sleeping');

                    showSuccess('😴 Mode sommeil activé - Agent en veille sécurisée');
                    console.log('😴 Mode sommeil activé avec succès');
                } else {
                    throw new Error('Erreur serveur sommeil');
                }
            } catch (error) {
                console.error('Erreur sommeil:', error);
                showError('Erreur lors de l\'activation du mode sommeil');
            }
        }

        // Réveiller l'agent
        function wakeupAgent() {
            console.log('☀️ Demande de réveil de l\'agent...');

            if (!isInSleepMode && !isInDeepHibernation) {
                showInfo('L\'agent est déjà éveillé');
                return;
            }

            // Déterminer le mode actuel pour la modal
            const currentMode = isInDeepHibernation ? 'hibernation profonde' : 'mode sommeil';
            const currentSleepModeElement = document.getElementById('currentSleepMode');

            if (currentSleepModeElement) {
                currentSleepModeElement.textContent = currentMode;
            }

            // Ouvrir la modal de réveil
            const wakeupModal = document.getElementById('wakeupModal');
            if (wakeupModal) {
                wakeupModal.style.display = 'flex';
                console.log('✅ Modal de réveil ouverte');
            }
        }

        // Fermer la modal de réveil
        function closeWakeupModal() {
            const wakeupModal = document.getElementById('wakeupModal');
            if (wakeupModal) {
                wakeupModal.style.display = 'none';

                // Vider les champs
                document.getElementById('securityCodeInput').value = '';
                document.getElementById('userAuthInput').value = '';
            }
        }

        // Confirmer le réveil avec le code saisi
        async function confirmWakeup() {
            const securityCode = document.getElementById('securityCodeInput').value;
            const userAuth = document.getElementById('userAuthInput').value;

            // Vérifier que le code est saisi
            if (!securityCode) {
                showError('Veuillez saisir le code de sécurité');
                return;
            }

            // Fermer la modal
            closeWakeupModal();

            // Procéder au réveil selon le mode
            if (isInDeepHibernation) {
                await performDeepHibernationWakeup(securityCode, userAuth);
            } else if (isInSleepMode) {
                await performNormalWakeup(securityCode, userAuth);
            }
        }

        // Réveil de l'hibernation profonde
        async function performDeepHibernationWakeup(securityCode, userAuth) {
            console.log('🌅 Réveil de l\'hibernation profonde...');

            try {
                const response = await fetch('/api/security/wakeup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'wakeup_hibernation',
                        code: securityCode,
                        user: userAuth || 'Jean-Luc'
                    })
                });

                if (response.ok) {
                    isInDeepHibernation = false;
                    isInSleepMode = false;

                    // Masquer les bannières
                    document.getElementById('hibernationBanner').classList.remove('active');
                    document.getElementById('sleepBanner').classList.remove('active');

                    // Réactiver toutes les fonctionnalités
                    enableAllFeatures();

                    // Mettre à jour l'état visuel
                    updateVisualState('awake');

                    showSuccess('🌅 RÉVEIL COMPLET - Agent Louna parfaitement réveillé de l\'hibernation profonde !');
                    console.log('🌅 Réveil hibernation réussi');
                } else {
                    throw new Error('Code de sécurité incorrect');
                }
            } catch (error) {
                console.error('Erreur réveil hibernation:', error);
                showError('Erreur lors du réveil - Vérifiez le code de sécurité');
            }
        }

        // Réveil du mode sommeil normal
        async function performNormalWakeup(securityCode, userAuth) {
            console.log('☀️ Réveil du mode sommeil...');

            try {
                const response = await fetch('/api/security/wakeup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'wakeup_sleep',
                        code: securityCode,
                        user: userAuth || 'Jean-Luc'
                    })
                });

                if (response.ok) {
                    isInSleepMode = false;
                    isInDeepHibernation = false;

                    // Masquer les bannières
                    document.getElementById('sleepBanner').classList.remove('active');
                    document.getElementById('hibernationBanner').classList.remove('active');

                    // Réactiver toutes les fonctionnalités
                    enableAllFeatures();

                    // Mettre à jour l'état visuel
                    updateVisualState('awake');

                    showSuccess('☀️ RÉVEIL RÉUSSI - Agent Louna parfaitement réveillé !');
                    console.log('☀️ Réveil sommeil réussi');
                } else {
                    throw new Error('Code de sécurité incorrect');
                }
            } catch (error) {
                console.error('Erreur réveil sommeil:', error);
                showError('Erreur lors du réveil - Vérifiez le code de sécurité');
            }
        }

        // ===== FONCTIONS DE GESTION DES FONCTIONNALITÉS =====

        // Désactiver TOUTES les fonctionnalités (hibernation profonde)
        function disableAllFeatures() {
            document.body.classList.add('hibernation-mode');

            const appCards = document.querySelectorAll('.quick-start-card');
            appCards.forEach(card => {
                card.style.opacity = '0.2';
                card.style.pointerEvents = 'none';
                card.style.filter = 'grayscale(100%)';
            });

            // Désactiver les liens de navigation SAUF les boutons de sécurité
            const navLinks = document.querySelectorAll('a:not(.security-btn):not(.btn-wakeup):not(.btn-hibernation)');
            navLinks.forEach(link => {
                link.style.pointerEvents = 'none';
                link.style.opacity = '0.3';
            });

            console.log('❄️ HIBERNATION PROFONDE - Toutes les fonctionnalités désactivées');
        }

        // Désactiver les fonctionnalités non essentielles (sommeil)
        function disableNonEssentialFeatures() {
            document.body.classList.add('sleep-mode');

            const appCards = document.querySelectorAll('.quick-start-card');
            appCards.forEach(card => {
                card.style.opacity = '0.5';
                card.style.filter = 'grayscale(50%)';
            });

            console.log('😴 MODE SOMMEIL - Fonctionnalités non essentielles désactivées');
        }

        // Réactiver toutes les fonctionnalités
        function enableAllFeatures() {
            document.body.classList.remove('sleep-mode');
            document.body.classList.remove('hibernation-mode');

            const appCards = document.querySelectorAll('.quick-start-card');
            appCards.forEach(card => {
                card.style.opacity = '1';
                card.style.pointerEvents = 'auto';
                card.style.filter = 'none';
            });

            // Réactiver les liens de navigation
            const navLinks = document.querySelectorAll('a');
            navLinks.forEach(link => {
                link.style.pointerEvents = 'auto';
                link.style.opacity = '1';
            });

            // Réinitialiser les boutons de sécurité
            document.querySelector('.btn-hibernation').classList.remove('active');
            document.querySelector('.btn-sleep').classList.remove('active');

            console.log('🌅 RÉVEIL COMPLET - Toutes les fonctionnalités réactivées');
        }

        // ===== FONCTIONS D'ÉTAT VISUEL =====

        // Mettre à jour l'état visuel global
        function updateVisualState(state) {
            const statusIcon = document.querySelector('.status-icon i');
            const statusText = document.getElementById('statusText');
            const globalStatus = document.getElementById('globalStatus');

            switch (state) {
                case 'hibernating':
                    statusIcon.className = 'fas fa-snowflake';
                    statusText.textContent = 'HIBERNATION PROFONDE';
                    globalStatus.style.borderColor = 'rgba(156, 39, 176, 0.5)';
                    statusIcon.style.color = '#9c27b0';
                    statusText.style.color = '#9c27b0';
                    break;

                case 'sleeping':
                    statusIcon.className = 'fas fa-moon';
                    statusText.textContent = 'MODE SOMMEIL';
                    globalStatus.style.borderColor = 'rgba(33, 150, 243, 0.5)';
                    statusIcon.style.color = '#2196f3';
                    statusText.style.color = '#2196f3';
                    break;

                case 'awake':
                default:
                    statusIcon.className = 'fas fa-brain';
                    statusText.textContent = 'AGENT ÉVEILLÉ';
                    globalStatus.style.borderColor = 'rgba(76, 175, 80, 0.5)';
                    statusIcon.style.color = '#4caf50';
                    statusText.style.color = '#4caf50';
                    break;
            }
        }

        // ===== AUTRES FONCTIONS DE SÉCURITÉ =====

        // Ouvrir la surveillance
        function openSurveillance() {
            console.log('🛡️ Ouverture surveillance sécurité...');
            window.open('/security-monitoring.html', '_blank');
        }

        // Ouvrir la sauvegarde
        function openBackup() {
            console.log('💾 Ouverture gestionnaire de sauvegarde...');
            window.open('/backup-manager.html', '_blank');
        }

        // Ouvrir le contrôle de la mémoire thermique
        function openMemoryControl() {
            console.log('🧠 Ouverture du contrôle de la mémoire thermique...');
            window.open('/memory-control.html', '_blank');
        }

        // ===== FONCTIONS DE MISE À JOUR DES STATISTIQUES =====

        // Mettre à jour les statistiques en temps réel
        function updateStats() {
            // Simulation de données temps réel
            const stats = {
                qi: 225,
                memoryTemp: (36.5 + Math.random() * 1.5).toFixed(1),
                neuronsActive: Math.floor(85 + Math.random() * 10),
                kyberAccelerators: `${Math.floor(6 + Math.random() * 4)}/16`,
                completion: '100%',
                learningRate: (92 + Math.random() * 6).toFixed(1)
            };

            // Mettre à jour les valeurs
            document.getElementById('qi-value').textContent = stats.qi;
            document.getElementById('memory-temp').textContent = stats.memoryTemp + '°C';
            document.getElementById('neurons-active').textContent = stats.neuronsActive;
            document.getElementById('kyber-accelerators').textContent = stats.kyberAccelerators;
            document.getElementById('completion').textContent = stats.completion;
            document.getElementById('learning-rate').textContent = stats.learningRate + '%';
        }

        // ===== FONCTIONS DE NOTIFICATION =====

        // Fonctions de notification simplifiées (SANS ALERTES POPUP)
        function showSuccess(message) {
            if (window.LounaNotify) {
                window.LounaNotify.success(message);
            } else {
                console.log('✅ SUCCESS:', message);
                // Pas d'alert - notification console seulement
            }
        }

        function showError(message) {
            if (window.LounaNotify) {
                window.LounaNotify.error(message);
            } else {
                console.log('❌ ERROR:', message);
                // Pas d'alert - notification console seulement
            }
        }

        function showWarning(message) {
            if (window.LounaNotify) {
                window.LounaNotify.warning(message);
            } else {
                console.log('⚠️ WARNING:', message);
                // Pas d'alert - notification console seulement
            }
        }

        function showInfo(message) {
            if (window.LounaNotify) {
                window.LounaNotify.info(message);
            } else {
                console.log('ℹ️ INFO:', message);
                // Pas d'alert - notification console seulement
            }
        }

        // ===== ANIMATIONS ET EFFETS =====

        // Ajouter les animations CSS manquantes
        const additionalStyles = document.createElement('style');
        additionalStyles.textContent = `
            @keyframes pulseViolet {
                0%, 100% { box-shadow: 0 0 15px rgba(156, 39, 176, 0.6); }
                50% { box-shadow: 0 0 25px rgba(156, 39, 176, 0.9); }
            }

            @keyframes flashOrange {
                0%, 100% { box-shadow: 0 0 15px rgba(255, 152, 0, 0.6); }
                50% { box-shadow: 0 0 25px rgba(255, 152, 0, 0.9); }
            }

            .hibernation-mode .quick-start-card {
                transition: all 0.5s ease;
            }

            .sleep-mode .quick-start-card {
                transition: all 0.3s ease;
            }
        `;
        document.head.appendChild(additionalStyles);

        // ===== INITIALISATION =====

        // Initialiser l'application au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Louna AI v2.1.0 - Hub Central initialisé');

            // Mettre à jour les statistiques
            updateStats();
            setInterval(updateStats, 5000); // Mise à jour toutes les 5 secondes

            // Animation d'entrée des cartes
            const cards = document.querySelectorAll('.quick-start-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });

            // Notification de bienvenue (désactivée pour éviter les popups)
            setTimeout(() => {
                console.log('🎯 Hub Central Louna AI v2.1.0 initialisé avec succès !');
                if (window.LounaNotify) {
                    // Notifications silencieuses dans la console seulement
                    console.log('✅ Toutes les interfaces sont opérationnelles');
                    console.log('✅ Mémoire thermique connectée et fonctionnelle');
                    console.log('✅ Agent Claude 4GB prêt à vous assister');
                }
            }, 1000);

            console.log('📱 Connexion téléphone disponible et prioritaire');
            console.log('⚡ QI: 225 - Système optimal');
            console.log('🔥 Mémoire thermique active');
            console.log('🛡️ Contrôles de sécurité opérationnels');
        });

        // Gestion des raccourcis clavier
        document.addEventListener('keydown', function(event) {
            // Ctrl+Shift+H = Hibernation
            if (event.ctrlKey && event.shiftKey && event.key === 'H') {
                event.preventDefault();
                activateHibernation();
            }

            // Ctrl+Shift+S = Sommeil
            if (event.ctrlKey && event.shiftKey && event.key === 'S') {
                event.preventDefault();
                activateSleep();
            }

            // Ctrl+Shift+W = Réveil
            if (event.ctrlKey && event.shiftKey && event.key === 'W') {
                event.preventDefault();
                wakeupAgent();
            }
        });

        console.log('🎯 Louna AI v2.1.0 - Système complet chargé avec succès !');
        console.log('📱 Connexion téléphone prioritaire activée');
        console.log('🔐 Contrôles de sécurité opérationnels (Code: 2338)');
        console.log('⚡ QI 225 - Performance optimale');
    </script>
</body>
</html>