<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 REEL LOUNA AI V5 - Chat Interface</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.4);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #ffffff;
        }

        .header-stats {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .qi-320 { color: #00ff00; }
        .memories { color: #ff69b4; }
        .temp { color: #ffa500; }
        .zone { color: #00bcd4; }

        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 10px;
        }

        .sidebar-btn {
            width: 100%;
            background: rgba(233, 30, 99, 0.2);
            border: 1px solid #e91e63;
            color: #ffffff;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .sidebar-btn:hover {
            background: rgba(233, 30, 99, 0.4);
            transform: translateY(-2px);
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.02);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid #e91e63;
            backdrop-filter: blur(10px);
        }

        .message.louna {
            border-left-color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .message-icon {
            font-size: 1.2rem;
        }

        .message-sender {
            font-weight: bold;
            color: #e91e63;
        }

        .message.louna .message-sender {
            color: #00ff00;
        }

        .message-content {
            line-height: 1.5;
            color: #ffffff;
        }

        .input-area {
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 12px 15px;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: #ffffff;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.4);
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: rgba(255, 105, 180, 0.2);
            border: 1px solid #ff69b4;
            color: #ff69b4;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .quick-btn:hover {
            background: rgba(255, 105, 180, 0.4);
            transform: translateY(-2px);
        }

        .connection-status {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-indicator {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-connected { background: rgba(0, 255, 0, 0.2); color: #00ff00; }
        .status-micro { background: rgba(255, 165, 0, 0.2); color: #ffa500; }
        .status-transfer { background: rgba(0, 188, 212, 0.2); color: #00bcd4; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="header-title">🧠 REEL LOUNA AI V5</div>
            <div class="header-title">💖 LOUNA-AI</div>
        </div>
        <div class="header-stats">
            <div class="stat-item qi-320">QI 320</div>
            <div class="stat-item memories">148 Mémoires</div>
            <div class="stat-item temp">67.4°C</div>
            <div class="stat-item zone">Zone 5</div>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <button class="sidebar-btn">🧠 Cerveau 3D Vivant</button>
            <button class="sidebar-btn">💭 Pensées & Émotions</button>
            <button class="sidebar-btn">🔥 Test QI Avancé</button>
            <button class="sidebar-btn">📊 Test Live Ultime</button>
            <button class="sidebar-btn">🎨 Génération IA</button>
            <button class="sidebar-btn">🎵 Cours Langage Naturel</button>
            <button class="sidebar-btn">📚 Présentation</button>
            <button class="sidebar-btn">⚙️ Configuration</button>
        </div>

        <div class="chat-area">
            <div class="chat-messages" id="chatMessages">
                <div class="message louna">
                    <div class="message-header">
                        <i class="fas fa-brain message-icon"></i>
                        <span class="message-sender">LOUNA-AI</span>
                    </div>
                    <div class="message-content">
                        🧠 Bonjour ! Je suis LOUNA-AI, votre assistant intelligent. Mon QI actuel est de 372 et je suis prête à vous aider ! (Via propagation neuronale) ⚡
                        Accéléré par 21 accélérateurs Kyber ⚡. Mémoire fluide active (cohérence: 78.2%)
                    </div>
                </div>
                
                <div class="message">
                    <div class="message-header">
                        <i class="fas fa-user message-icon"></i>
                        <span class="message-sender">Vous</span>
                    </div>
                    <div class="message-content">Salut</div>
                </div>

                <div class="message louna">
                    <div class="message-header">
                        <i class="fas fa-brain message-icon"></i>
                        <span class="message-sender">LOUNA-AI</span>
                    </div>
                    <div class="message-content">
                        🧠 Aucun résultat trouvé pour "donnez moi la capital de france". Essayez de reformuler votre recherche. ⚡ Accéléré par 21 accélérateurs Kyber ⚡. Mémoire fluide active (cohérence: 78.3%)
                    </div>
                </div>
            </div>

            <div class="input-area">
                <div class="connection-status">
                    <div class="status-indicator status-connected">✅ Connexion sécurisée</div>
                    <div class="status-indicator status-micro">🎤 Micro off</div>
                    <div class="status-indicator status-transfer">📡 Haut-parleur actif</div>
                    <div class="status-indicator status-transfer">📶 Transfert disponible</div>
                </div>

                <div class="quick-actions">
                    <button class="quick-btn">📶 WiFi</button>
                    <button class="quick-btn">📱 Bluetooth</button>
                    <button class="quick-btn">📡 AirDrop</button>
                    <button class="quick-btn">🧪 Test QI Rapide</button>
                    <button class="quick-btn">🔍 Analyse Complète</button>
                    <button class="quick-btn">⭐ DMA Langage</button>
                    <button class="quick-btn">🔥 Test Créativité</button>
                    <button class="quick-btn">🧠 Élixir</button>
                    <button class="quick-btn">🔥 TEST LIVE ULTIME</button>
                </div>

                <div class="input-container">
                    <input type="text" class="message-input" placeholder="Posez-moi une question ou utilisez les tests rapides ci-dessus ! (Ctrl+V pour coller)" id="messageInput">
                    <button class="send-btn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> Envoyer
                    </button>
                    <button class="send-btn" style="background: linear-gradient(135deg, #ff69b4, #e91e63);">
                        <i class="fas fa-microphone"></i> Micro
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addMessage('user', message);
                input.value = '';
                
                // Simuler une réponse de Louna
                setTimeout(() => {
                    const responses = [
                        "🧠 Excellente question ! Mon QI de 320 me permet d'analyser cela en profondeur. ⚡ Accéléré par 21 accélérateurs Kyber ⚡",
                        "💭 Analyse en cours... Mémoire thermique à 67.4°C, zone 5 active. Traitement neuronal optimisé !",
                        "🔥 Réponse générée avec 148 mémoires actives. Cohérence neuronale: 78.2% ⚡"
                    ];
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    addMessage('louna', randomResponse);
                }, 1000);
            }
        }

        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender === 'louna' ? 'louna' : ''}`;
            
            const icon = sender === 'louna' ? 'fas fa-brain' : 'fas fa-user';
            const senderName = sender === 'louna' ? 'LOUNA-AI' : 'Vous';
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    <i class="${icon} message-icon"></i>
                    <span class="message-sender">${senderName}</span>
                </div>
                <div class="message-content">${content}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Envoyer message avec Entrée
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Simulation de mise à jour des stats
        setInterval(() => {
            const temp = document.querySelector('.temp');
            const newTemp = (67.0 + Math.random() * 1.0).toFixed(1);
            temp.textContent = newTemp + '°C';
        }, 5000);
    </script>
</body>
</html>
