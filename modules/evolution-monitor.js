/**
 * Moniteur de performance pour l'application Louna
 *
 * Ce module surveille les performances des agents et de la mémoire thermique
 * et génère des alertes en cas de problèmes détectés.
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);

// Configuration
const API_BASE_URL = 'http://localhost:3005/api';
const CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

// Variables pour stocker les références à la mémoire thermique et au gestionnaire d'agents
let thermalMemory = null;
let agentManager = null;

/**
 * Initialise le moniteur de performance
 * @param {Object} memory - Instance de la mémoire thermique
 * @param {Object} manager - Instance du gestionnaire d'agents
 */
function initializePerformanceMonitor(memory, manager) {
    thermalMemory = memory;
    agentManager = manager;

    console.log('Moniteur de performance initialisé');

    // Démarrer la vérification périodique
    startPeriodicCheck();
}

/**
 * Démarre la vérification périodique des performances
 */
function startPeriodicCheck() {
    // Vérifier immédiatement
    checkPerformance();

    // Planifier les vérifications périodiques
    setInterval(checkPerformance, CHECK_INTERVAL);
}

/**
 * Vérifie les performances globales
 */
async function checkPerformance() {
    try {
        console.log('Vérification des performances...');

        // Charger la configuration des alertes
        const config = await loadAlertsConfig();

        // Vérifier si la vérification automatique est activée
        if (!config.monitoring.enableAutoCheck) {
            console.log('Vérification automatique désactivée');
            return;
        }

        // Vérifier les différents composants
        await checkMemoryPerformance(config.thresholds);
        await checkAcceleratorPerformance(config.thresholds);
        await checkAgentPerformance(config.thresholds);

        console.log('Vérification des performances terminée');
    } catch (error) {
        console.error('Erreur lors de la vérification des performances:', error);
    }
}

/**
 * Charge la configuration des alertes
 * @returns {Promise<Object>} - Configuration des alertes
 */
async function loadAlertsConfig() {
    try {
        const configPath = path.join(__dirname, 'data', 'config', 'performance-alerts.json');

        if (await existsAsync(configPath)) {
            const data = await readFileAsync(configPath, 'utf8');
            return JSON.parse(data);
        }

        return {
            thresholds: {},
            notifications: {
                enableSounds: true,
                enableBrowserNotifications: true,
                dismissDuration: 86400000
            },
            monitoring: {
                checkInterval: 300000,
                enableAutoCheck: true
            }
        };
    } catch (error) {
        console.error('Erreur lors du chargement de la configuration des alertes:', error);
        return {
            thresholds: {},
            notifications: {
                enableSounds: true,
                enableBrowserNotifications: true,
                dismissDuration: 86400000
            },
            monitoring: {
                checkInterval: 300000,
                enableAutoCheck: true
            }
        };
    }
}

/**
 * Vérifie les performances de la mémoire thermique
 * @param {Object} thresholds - Seuils d'alerte
 */
async function checkMemoryPerformance(thresholds) {
    try {
        // Récupérer les statistiques de la mémoire
        let memoryStats;

        if (thermalMemory) {
            memoryStats = thermalMemory.getMemoryStats();
        } else {
            const response = await axios.get(`${API_BASE_URL}/thermal/memory/stats`);

            if (response.data.success) {
                memoryStats = response.data.stats;
            } else {
                throw new Error(response.data.error);
            }
        }

        // Vérifier la température moyenne
        if (memoryStats.averageTemperature > thresholds.memory.temperature.high) {
            await createAlert('memory', 'temperature_high', 'critical',
                `Température moyenne élevée (${memoryStats.averageTemperature.toFixed(2)})`,
                'La température moyenne de la mémoire est trop élevée, ce qui peut indiquer une surcharge d\'informations importantes.');
        } else if (memoryStats.averageTemperature < thresholds.memory.temperature.low) {
            await createAlert('memory', 'temperature_low', 'warning',
                `Température moyenne basse (${memoryStats.averageTemperature.toFixed(2)})`,
                'La température moyenne de la mémoire est trop basse, ce qui peut indiquer un manque d\'informations importantes.');
        }

        // Vérifier les capacités des zones de mémoire
        const zones = ['instant', 'shortTerm', 'workingMemory', 'mediumTerm', 'longTerm', 'dreamMemory'];

        for (const zone of zones) {
            if (memoryStats[`${zone}Capacity`]) {
                const capacity = memoryStats[`${zone}Entries`] / memoryStats[`${zone}Capacity`];

                if (capacity > thresholds.memory.capacity.critical) {
                    await createAlert('memory', `capacity_${zone}_critical`, 'critical',
                        `Capacité critique de la zone ${zone} (${Math.round(capacity * 100)}%)`,
                        `La zone de mémoire ${zone} est presque pleine, ce qui peut entraîner des pertes d'informations.`);
                } else if (capacity > thresholds.memory.capacity.warning) {
                    await createAlert('memory', `capacity_${zone}_warning`, 'warning',
                        `Capacité élevée de la zone ${zone} (${Math.round(capacity * 100)}%)`,
                        `La zone de mémoire ${zone} se remplit rapidement.`);
                }
            }
        }

        // Vérifier le dernier cycle de mémoire
        if (memoryStats.lastCycleTime) {
            const timeSinceLastCycle = Date.now() - memoryStats.lastCycleTime;

            if (timeSinceLastCycle > thresholds.memory.cycleTime) {
                await createAlert('memory', 'cycle_time', 'warning',
                    'Aucun cycle de mémoire récent',
                    `Aucun cycle de mémoire n'a été effectué depuis ${Math.round(timeSinceLastCycle / 60000)} minutes.`);
            }
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances de la mémoire:', error);
    }
}

/**
 * Vérifie les performances des accélérateurs Kyber
 * @param {Object} thresholds - Seuils d'alerte
 */
async function checkAcceleratorPerformance(thresholds) {
    try {
        // Récupérer les statistiques des accélérateurs
        let acceleratorStats;

        const response = await axios.get(`${API_BASE_URL}/kyber/stats`);

        if (response.data.success) {
            acceleratorStats = response.data.stats;
        } else {
            throw new Error(response.data.error);
        }

        // Vérifier l'efficacité
        if (acceleratorStats.efficiency < thresholds.accelerators.efficiency.critical) {
            await createAlert('accelerators', 'efficiency_critical', 'critical',
                `Efficacité critique des accélérateurs (${Math.round(acceleratorStats.efficiency * 100)}%)`,
                'L\'efficacité des accélérateurs Kyber est très basse, ce qui peut affecter les performances globales.');
        } else if (acceleratorStats.efficiency < thresholds.accelerators.efficiency.warning) {
            await createAlert('accelerators', 'efficiency_warning', 'warning',
                `Efficacité réduite des accélérateurs (${Math.round(acceleratorStats.efficiency * 100)}%)`,
                'L\'efficacité des accélérateurs Kyber est en baisse.');
        }

        // Vérifier la stabilité
        if (acceleratorStats.stability < thresholds.accelerators.stability.critical) {
            await createAlert('accelerators', 'stability_critical', 'critical',
                `Stabilité critique des accélérateurs (${Math.round(acceleratorStats.stability * 100)}%)`,
                'La stabilité des accélérateurs Kyber est très basse, ce qui peut causer des comportements imprévisibles.');
        } else if (acceleratorStats.stability < thresholds.accelerators.stability.warning) {
            await createAlert('accelerators', 'stability_warning', 'warning',
                `Stabilité réduite des accélérateurs (${Math.round(acceleratorStats.stability * 100)}%)`,
                'La stabilité des accélérateurs Kyber est en baisse.');
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances des accélérateurs:', error);
    }
}

/**
 * Vérifie les performances des agents
 * @param {Object} thresholds - Seuils d'alerte
 */
async function checkAgentPerformance(thresholds) {
    try {
        // Récupérer les statistiques des agents
        const response = await axios.get(`${API_BASE_URL}/performance/stats`);

        if (!response.data.success) {
            throw new Error(response.data.error);
        }

        const agentStats = response.data.stats;

        // Vérifier les performances de chaque agent
        for (const agentId in agentStats) {
            const agent = agentStats[agentId];

            // Vérifier la performance
            if (agent.performance < thresholds.agents.performance.critical) {
                await createAlert('agents', `performance_${agentId}_critical`, 'critical',
                    `Performance critique de l'agent ${agent.name || agentId} (${Math.round(agent.performance * 100)}%)`,
                    'La performance de l\'agent est très basse, ce qui peut affecter la qualité des réponses.');
            } else if (agent.performance < thresholds.agents.performance.warning) {
                await createAlert('agents', `performance_${agentId}_warning`, 'warning',
                    `Performance réduite de l'agent ${agent.name || agentId} (${Math.round(agent.performance * 100)}%)`,
                    'La performance de l\'agent est en baisse.');
            }

            // Vérifier le temps de réponse
            if (agent.responseTime > thresholds.agents.responseTime.critical) {
                await createAlert('agents', `response_time_${agentId}_critical`, 'critical',
                    `Temps de réponse critique de l'agent ${agent.name || agentId} (${agent.responseTime}ms)`,
                    'Le temps de réponse de l\'agent est très élevé, ce qui peut affecter l\'expérience utilisateur.');
            } else if (agent.responseTime > thresholds.agents.responseTime.warning) {
                await createAlert('agents', `response_time_${agentId}_warning`, 'warning',
                    `Temps de réponse élevé de l'agent ${agent.name || agentId} (${agent.responseTime}ms)`,
                    'Le temps de réponse de l\'agent est en hausse.');
            }
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances des agents:', error);
    }
}

/**
 * Crée une alerte
 * @param {string} category - Catégorie de l'alerte
 * @param {string} id - Identifiant unique de l'alerte
 * @param {string} severity - Sévérité de l'alerte (critical, warning, info)
 * @param {string} title - Titre de l'alerte
 * @param {string} message - Message détaillé de l'alerte
 */
async function createAlert(category, id, severity, title, message) {
    try {
        await axios.post(`${API_BASE_URL}/alerts/create`, {
            category,
            id,
            severity,
            title,
            message
        });
    } catch (error) {
        console.error('Erreur lors de la création de l\'alerte:', error);
    }
}

// Exporter le module
module.exports = initializePerformanceMonitor;
