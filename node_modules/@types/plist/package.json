{"name": "@types/plist", "version": "3.0.5", "description": "TypeScript definitions for plist", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/plist", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/higuri"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/plist"}, "scripts": {}, "dependencies": {"@types/node": "*", "xmlbuilder": ">=11.0.1"}, "typesPublisherContentHash": "447de0d797b7b91d360cc25c604e462258bc6e61eabea7eb28e7793de2c3ddc6", "typeScriptVersion": "4.5"}